import 'package:flutter_test/flutter_test.dart';
import 'package:power_up/features/app_constants/data/models/app_constants_model.dart';

void main() {
  group('AppConstantsModel', () {
    test('should create model from JSON correctly', () {
      // Arrange
      final json = {
        'id': 'test-id',
        'constants': {
          'features': {
            'socialLogin': true,
            'pushNotifications': false,
          },
          'api': {
            'baseUrl': 'https://api.example.com',
            'connectTimeout': 3000,
          },
        },
        'createdAt': '2023-01-01T00:00:00.000Z',
        'updatedAt': '2023-01-02T00:00:00.000Z',
      };

      // Act
      final model = AppConstantsModel.fromJson(json);

      // Assert
      expect(model.id, 'test-id');
      expect(model.constants['features']['socialLogin'], true);
      expect(model.constants['features']['pushNotifications'], false);
      expect(model.constants['api']['baseUrl'], 'https://api.example.com');
      expect(model.constants['api']['connectTimeout'], 3000);
      expect(model.createdAt, DateTime.parse('2023-01-01T00:00:00.000Z'));
      expect(model.updatedAt, DateTime.parse('2023-01-02T00:00:00.000Z'));
    });

    test('should convert model to JSON correctly', () {
      // Arrange
      final model = AppConstantsModel(
        id: 'test-id',
        constants: const {
          'features': {
            'socialLogin': true,
            'pushNotifications': false,
          },
        },
        createdAt: DateTime.parse('2023-01-01T00:00:00.000Z'),
        updatedAt: DateTime.parse('2023-01-02T00:00:00.000Z'),
      );

      // Act
      final json = model.toJson();

      // Assert
      expect(json['id'], 'test-id');
      expect(json['constants']['features']['socialLogin'], true);
      expect(json['constants']['features']['pushNotifications'], false);
      expect(json['createdAt'], '2023-01-01T00:00:00.000Z');
      expect(json['updatedAt'], '2023-01-02T00:00:00.000Z');
    });

    test('should create default constants correctly', () {
      // Act
      final model = AppConstantsModel.withDefaults();

      // Assert
      expect(model.id, 'default');
      expect(model.getBool('features.socialLogin'), true);
      expect(model.getBool('features.pushNotifications'), true);
      expect(model.getBool('features.premiumFeatures'), true);
      expect(model.getString('api.baseUrl'), 'http://localhost:3000/api');
      expect(model.getInt('api.connectTimeout'), 5000);
      expect(model.getString('ui.appName'), 'Power Up');
      expect(model.getString('ui.appVersion'), '1.0.0');
      expect(model.getInt('gamification.xpPerHabit'), 10);
      expect(model.getInt('limits.maxHabitsPerUser'), 50);
    });

    test('should get nested constants correctly', () {
      // Arrange
      final model = AppConstantsModel.withDefaults();

      // Act & Assert
      expect(model.getBool('features.socialLogin'), true);
      expect(model.getBool('features.nonExistent', false), false);
      expect(model.getString('api.baseUrl'), 'http://localhost:3000/api');
      expect(model.getString('api.nonExistent', 'default'), 'default');
      expect(model.getInt('gamification.xpPerHabit'), 10);
      expect(model.getInt('gamification.nonExistent', 999), 999);
    });

    test('should handle missing or null values gracefully', () {
      // Arrange
      final json = {
        'id': 'test-id',
        'constants': {},
      };

      // Act
      final model = AppConstantsModel.fromJson(json);

      // Assert
      expect(model.id, 'test-id');
      expect(model.constants, isEmpty);
      expect(model.getBool('nonExistent.key'), false);
      expect(model.getString('nonExistent.key', 'default'), 'default');
      expect(model.getInt('nonExistent.key', 42), 42);
    });
  });
}
