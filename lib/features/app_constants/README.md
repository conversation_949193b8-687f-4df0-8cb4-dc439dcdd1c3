# App Constants Feature

This feature provides dynamic app constants management that fetches configuration from the backend admin panel and makes it available throughout the Flutter app.

## Overview

The App Constants feature allows administrators to control various app settings from a backend admin panel, which are then fetched and used throughout the Flutter app. This enables dynamic configuration without requiring app updates.

## Architecture

The feature follows Clean Architecture principles with the following layers:

### Domain Layer

- **Entities**: `AppConstantsEntity` - Core business object representing app constants
- **Repositories**: `AppConstantsRepository` - Abstract interface for data operations
- **Use Cases**:
  - `GetAppConstantsUseCase` - Fetch constants from repository
  - `GetCachedAppConstantsUseCase` - Get cached constants
  - `ClearAppConstantsCacheUseCase` - Clear cached constants

### Data Layer

- **Models**: `AppConstantsModel` - Data model with JSON serialization
- **Data Sources**: `AppConstantsRemoteDataSource` - Fetches from API endpoint `/api/admin/app-constants`
- **Repository Implementation**: `AppConstantsRepositoryImpl` - Handles caching and network logic

### Presentation Layer

- **Controllers**: `AppConstantsController` - GetX controller for UI state management
- **Service**: `AppConstantsService` - Global service for app-wide access

## Features

### Dynamic Constants

- **Feature Flags**: Enable/disable features like social login, push notifications, premium features
- **API Configuration**: Base URL, timeouts, and other API settings
- **UI Configuration**: App name, version, spacing, logo sizes
- **Gamification**: XP rewards, level thresholds
- **Limits**: Maximum items per user (habits, tasks, challenges)
- **Store URLs**: App Store and Play Store URLs for app rating

### Caching

- In-memory caching with 1-hour expiration
- Fallback to cached data when network is unavailable
- Default constants when no cache or network available

### Settings UI

- View current constants in Settings screen
- Refresh constants manually
- Clear cache and refresh
- Color-coded values (green/red for booleans, blue for numbers)

## Usage

### Accessing Constants

```dart
// Get the service
final appConstantsService = Get.find<AppConstantsService>();

// Access feature flags
bool socialLoginEnabled = appConstantsService.enableSocialLogin;
bool pushNotificationsEnabled = appConstantsService.enablePushNotifications;

// Access API configuration
String apiBaseUrl = appConstantsService.apiBaseUrl;
int connectTimeout = appConstantsService.apiConnectTimeout;

// Access UI configuration
String appName = appConstantsService.appName;
String appVersion = appConstantsService.appVersion;
double pageSpacing = appConstantsService.pageSpacing;

// Access gamification settings
int xpPerHabit = appConstantsService.xpPerHabit;
int levelUpThreshold = appConstantsService.levelUpThreshold;

// Access limits
int maxHabitsPerUser = appConstantsService.maxHabitsPerUser;

// Access store URLs
String appStoreUrl = appConstantsService.appStoreUrl;
String playStoreUrl = appConstantsService.playStoreUrl;

// Generic access with dot notation
String customValue = appConstantsService.getString('custom.nested.key', 'defaultValue');
bool customFlag = appConstantsService.getBool('features.customFeature', false);
int customNumber = appConstantsService.getInt('limits.customLimit', 100);
```

### Reactive UI Updates

```dart
// Use Obx for reactive updates
Obx(() => Text(
  'Welcome to ${appConstantsService.appName}!',
  style: TextStyle(fontSize: appConstantsService.getDouble('ui.titleSize', 24.0)),
))

// Check if feature is enabled
if (appConstantsService.enablePremiumFeatures) {
  // Show premium content
}
```

### Manual Refresh

```dart
final controller = Get.find<AppConstantsController>();

// Refresh constants
await controller.refreshConstants();

// Clear cache and refresh
await controller.clearCacheAndRefresh();

// Check loading state
if (controller.isLoading) {
  // Show loading indicator
}

// Handle errors
if (controller.errorMessage.isNotEmpty) {
  // Show error message
}
```

## Backend Integration

The feature expects the backend to provide an endpoint:

```
GET /api/admin/app-constants
Authorization: Bearer <admin-token>
```

Response format:

```json
{
  "id": "constants-id",
  "constants": {
    "features": {
      "socialLogin": true,
      "pushNotifications": true,
      "premiumFeatures": true,
      "darkMode": true,
      "offlineMode": false
    },
    "api": {
      "baseUrl": "https://api.example.com",
      "connectTimeout": 5000,
      "receiveTimeout": 10000
    },
    "ui": {
      "appName": "Power Up",
      "appVersion": "1.0.0",
      "splashLogoSize": 120.0,
      "appBarLogoHeight": 32.0,
      "pageSpacing": 24.0,
      "sectionSpacing": 16.0
    },
    "gamification": {
      "xpPerHabit": 10,
      "xpPerTask": 15,
      "xpPerChallenge": 50,
      "levelUpThreshold": 100
    },
    "limits": {
      "maxHabitsPerUser": 50,
      "maxTasksPerUser": 100,
      "maxChallengesPerUser": 10
    },
    "store": {
      "appStoreUrl": "https://apps.apple.com/app/power-up/id123456789",
      "playStoreUrl": "https://play.google.com/store/apps/details?id=com.example.powerup"
    }
  },
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

## Error Handling

- Network errors: Falls back to cached data or default constants
- Server errors: Falls back to cached data or default constants
- Invalid data: Uses default values for missing or invalid fields
- No authentication: Uses default constants

## Testing

Run tests with:

```bash
flutter test test/features/app_constants/
```

## Dependencies

- `get`: State management and dependency injection
- `dartz`: Functional programming (Either type)
- `dio`: HTTP client
- `equatable`: Value equality
