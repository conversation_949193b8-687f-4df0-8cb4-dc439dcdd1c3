import '../../domain/entities/app_constants_entity.dart';

/// Data model for app constants with JSON serialization support
class AppConstantsModel extends AppConstantsEntity {
  const AppConstantsModel({
    required super.id,
    required super.constants,
    required super.createdAt,
    required super.updatedAt,
  });

  /// Create AppConstantsModel from JSON
  factory AppConstantsModel.fromJson(Map<String, dynamic> json) {
    return AppConstantsModel(
      id: json['id'] ?? '',
      constants: Map<String, dynamic>.from(json['constants'] ?? {}),
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'])
              : DateTime.now(),
    );
  }

  /// Convert AppConstantsModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'constants': constants,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated values
  AppConstantsModel copyWith({
    String? id,
    Map<String, dynamic>? constants,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppConstantsModel(
      id: id ?? this.id,
      constants: constants ?? this.constants,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Create from entity
  factory AppConstantsModel.fromEntity(AppConstantsEntity entity) {
    return AppConstantsModel(
      id: entity.id,
      constants: entity.constants,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }

  /// Create empty/default constants
  factory AppConstantsModel.empty() {
    return AppConstantsModel(
      id: '',
      constants: const {},
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Create with default fallback constants
  factory AppConstantsModel.withDefaults() {
    final now = DateTime.now();
    return AppConstantsModel(
      id: 'default',
      constants: const {
        'features': {
          'socialLogin': true,
          'pushNotifications': true,
          'premiumFeatures': true,
          'darkMode': true,
          'offlineMode': false,
        },
        'api': {
          'baseUrl': 'http://localhost:3000/api',
          'connectTimeout': 5000,
          'receiveTimeout': 10000,
        },
        'ui': {
          'appName': 'Power Up',
          'appVersion': '1.0.0',
          'splashLogoSize': 120.0,
          'appBarLogoHeight': 32.0,
          'pageSpacing': 24.0,
          'sectionSpacing': 16.0,
        },
        'gamification': {
          'xpPerHabit': 10,
          'xpPerTask': 15,
          'xpPerChallenge': 50,
          'levelUpThreshold': 100,
        },
        'limits': {
          'maxHabitsPerUser': 50,
          'maxTasksPerUser': 100,
          'maxChallengesPerUser': 10,
        },
      },
      createdAt: now,
      updatedAt: now,
    );
  }
}
