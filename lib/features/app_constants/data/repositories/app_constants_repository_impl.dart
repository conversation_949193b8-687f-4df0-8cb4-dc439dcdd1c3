import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/app_constants_entity.dart';
import '../../domain/repositories/app_constants_repository.dart';
import '../datasources/app_constants_remote_data_source.dart';
import '../models/app_constants_model.dart';

/// Implementation of app constants repository
class AppConstantsRepositoryImpl implements AppConstantsRepository {
  final AppConstantsRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;

  // In-memory cache for app constants
  AppConstantsModel? _cachedConstants;
  DateTime? _cacheTime;
  
  // Cache duration - 1 hour
  static const Duration _cacheDuration = Duration(hours: 1);

  AppConstantsRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, AppConstantsEntity>> getAppConstants() async {
    // Check if we have valid cached data
    if (_isCacheValid()) {
      if (kDebugMode) {
        print('Returning cached app constants');
      }
      return Right(_cachedConstants!);
    }

    // Check network connectivity
    if (await networkInfo.isConnected) {
      try {
        if (kDebugMode) {
          print('Fetching app constants from remote source');
        }
        
        final remoteConstants = await remoteDataSource.getAppConstants();
        
        // Cache the result
        _cachedConstants = remoteConstants;
        _cacheTime = DateTime.now();
        
        if (kDebugMode) {
          print('Successfully fetched and cached app constants');
        }
        
        return Right(remoteConstants);
      } on ServerException catch (e) {
        if (kDebugMode) {
          print('Server exception: ${e.message}');
        }
        
        // If we have cached data, return it as fallback
        if (_cachedConstants != null) {
          if (kDebugMode) {
            print('Returning cached constants as fallback');
          }
          return Right(_cachedConstants!);
        }
        
        return Left(ServerFailure(message: e.message));
      } on NetworkException catch (e) {
        if (kDebugMode) {
          print('Network exception: ${e.message}');
        }
        
        // If we have cached data, return it as fallback
        if (_cachedConstants != null) {
          if (kDebugMode) {
            print('Returning cached constants as fallback');
          }
          return Right(_cachedConstants!);
        }
        
        return Left(NetworkFailure(message: e.message));
      } catch (e) {
        if (kDebugMode) {
          print('Unexpected error: $e');
        }
        
        // If we have cached data, return it as fallback
        if (_cachedConstants != null) {
          if (kDebugMode) {
            print('Returning cached constants as fallback');
          }
          return Right(_cachedConstants!);
        }
        
        return Left(ServerFailure(message: 'Unexpected error occurred'));
      }
    } else {
      if (kDebugMode) {
        print('No network connection');
      }
      
      // If we have cached data, return it
      if (_cachedConstants != null) {
        if (kDebugMode) {
          print('Returning cached constants (offline)');
        }
        return Right(_cachedConstants!);
      }
      
      // No network and no cache - return default constants
      if (kDebugMode) {
        print('No network and no cache - returning default constants');
      }
      
      final defaultConstants = AppConstantsModel.withDefaults();
      _cachedConstants = defaultConstants;
      _cacheTime = DateTime.now();
      
      return Right(defaultConstants);
    }
  }

  @override
  AppConstantsEntity? getCachedAppConstants() {
    if (_isCacheValid()) {
      return _cachedConstants;
    }
    return null;
  }

  @override
  Future<void> clearCache() async {
    if (kDebugMode) {
      print('Clearing app constants cache');
    }
    _cachedConstants = null;
    _cacheTime = null;
  }

  /// Check if cached data is still valid
  bool _isCacheValid() {
    if (_cachedConstants == null || _cacheTime == null) {
      return false;
    }
    
    final now = DateTime.now();
    final isValid = now.difference(_cacheTime!).compareTo(_cacheDuration) < 0;
    
    if (kDebugMode) {
      print('Cache valid: $isValid (cached at: $_cacheTime, now: $now)');
    }
    
    return isValid;
  }
}
