import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../core/presentation/screens/main_layout_screen.dart';
import '../../../authentication/presentation/controllers/auth_controller.dart';
import '../../../notifications/presentation/controllers/notification_controller.dart';
import '../../../user_management/presentation/controllers/user_management_controller.dart';
import '../controllers/settings_controller.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final settingsController = Get.find<SettingsController>();
    final authController = Get.find<AuthController>();
    final notificationController = Get.find<NotificationController>();
    final userManagementController = Get.find<UserManagementController>();

    return MainLayoutScreen(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Text(
              'Settings',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Customize your Power Up experience',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),

            const SizedBox(height: 24),

            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  // Account Section
                  _buildSectionHeader(context, 'Account'),
                  _buildSettingsItem(
                    context,
                    icon: Icons.person_outline,
                    title: 'Profile',
                    subtitle: 'Manage your profile information',
                    onTap: () {
                      Get.toNamed(AppRoutes.profile);
                    },
                  ),

                  const SizedBox(height: 24),

                  // Preferences Section
                  _buildSectionHeader(context, 'Preferences'),
                  _buildSettingsItem(
                    context,
                    icon: Icons.notifications_outlined,
                    title: 'notifications'.tr,
                    subtitle: 'manage_notifications'.tr,
                    onTap: () {
                      Get.toNamed(AppRoutes.notifications);
                    },
                  ),
                  _buildSettingsItem(
                    context,
                    icon: Icons.palette_outlined,
                    title: 'theme'.tr,
                    subtitle: 'choose_theme'.tr,
                    trailing: Obx(
                      () => Switch(
                        value: settingsController.isDarkMode,
                        onChanged: (value) {
                          settingsController.updateThemeMode(
                            value ? 'dark' : 'light',
                          );
                        },
                      ),
                    ),
                    onTap: settingsController.toggleThemeMode,
                  ),
                  Obx(
                    () => _buildSettingsItem(
                      context,
                      icon: Icons.language,
                      title: 'language'.tr,
                      subtitle: settingsController.currentLanguageDisplayName,
                      trailing: DropdownButton<String>(
                        value: settingsController.currentLanguageCode,
                        items:
                            SettingsController.supportedLanguages.entries
                                .map(
                                  (entry) => DropdownMenuItem<String>(
                                    value: entry.key,
                                    child: Text(entry.value['name']!),
                                  ),
                                )
                                .toList(),
                        onChanged: (String? newLanguageCode) {
                          if (newLanguageCode != null) {
                            final countryCode =
                                SettingsController
                                    .supportedLanguages[newLanguageCode]?['country'] ??
                                'US';
                            settingsController.updateLanguage(
                              newLanguageCode,
                              countryCode,
                            );
                          }
                        },
                        underline: const SizedBox.shrink(),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // App Section
                  // _buildSectionHeader(context, 'App'),
                  // _buildSettingsItem(
                  //   context,
                  //   icon: Icons.settings_applications_outlined,
                  //   title: 'App Constants',
                  //   subtitle: 'Manage app configuration',
                  //   trailing: Obx(
                  //     () =>
                  //         appConstantsController.isLoading
                  //             ? const SizedBox(
                  //               width: 20,
                  //               height: 20,
                  //               child: CircularProgressIndicator(
                  //                 strokeWidth: 2,
                  //               ),
                  //             )
                  //             : IconButton(
                  //               icon: const Icon(Icons.refresh),
                  //               onPressed: () async {
                  //                 await appConstantsController
                  //                     .refreshConstants();
                  //                 if (appConstantsController
                  //                     .errorMessage
                  //                     .isEmpty) {
                  //                   appConstantsController.showSuccessMessage(
                  //                     'App constants refreshed successfully',
                  //                   );
                  //                 } else {
                  //                   appConstantsController.showErrorMessage(
                  //                     appConstantsController.errorMessage,
                  //                   );
                  //                 }
                  //               },
                  //             ),
                  //   ),
                  //   onTap: () {
                  //     _showAppConstantsDialog(context, appConstantsController);
                  //   },
                  // ),

                  // const SizedBox(height: 24),

                  // // Additional App Section items
                  // _buildSectionHeader(context, 'App'),
                  // _buildSettingsItem(
                  //   context,
                  //   icon: Icons.backup_outlined,
                  //   title: 'Backup & Sync',
                  //   subtitle: 'Sync your data across devices',
                  //   onTap: () {
                  //     // Navigate to backup settings
                  //   },
                  // ),
                  // _buildSettingsItem(
                  //   context,
                  //   icon: Icons.storage_outlined,
                  //   title: 'Storage',
                  //   subtitle: 'Manage app storage',
                  //   onTap: () {
                  //     // Navigate to storage settings
                  //   },
                  // ),
                  // _buildSettingsItem(
                  //   context,
                  //   icon: Icons.system_update_outlined,
                  //   title: 'App Version',
                  //   subtitle: 'v1.0.0 (Latest)',
                  //   onTap: () {
                  //     // Check for updates
                  //   },
                  // ),

                  // const SizedBox(height: 24),

                  // Support Section
                  _buildSectionHeader(context, 'Support'),
                  _buildSettingsItem(
                    context,
                    icon: Icons.help_outline,
                    title: 'Help & FAQ',
                    subtitle: 'Get help and find answers',
                    onTap: () => Get.toNamed('/help'),
                  ),
                  _buildSettingsItem(
                    context,
                    icon: Icons.feedback_outlined,
                    title: 'Send Feedback',
                    subtitle: 'Tell us what you think',
                    onTap: () => Get.toNamed('/feedback'),
                  ),
                  _buildSettingsItem(
                    context,
                    icon: Icons.star_outline,
                    title: 'Rate App',
                    subtitle: 'Rate us on the app store',
                    onTap: () => settingsController.rateApp(),
                  ),

                  const SizedBox(height: 24),

                  // Legal Section
                  _buildSectionHeader(context, 'Legal'),
                  _buildSettingsItem(
                    context,
                    icon: Icons.description_outlined,
                    title: 'Terms of Service',
                    subtitle: 'Read our terms',
                    onTap: () => Get.toNamed('/legal', arguments: 'terms'),
                  ),
                  _buildSettingsItem(
                    context,
                    icon: Icons.privacy_tip_outlined,
                    title: 'Privacy Policy',
                    subtitle: 'Read our privacy policy',
                    onTap: () => Get.toNamed('/legal', arguments: 'privacy'),
                  ),

                  const SizedBox(height: 24),

                  // Privacy & Data Section
                  _buildSectionHeader(context, 'Privacy & Data'),
                  Obx(() {
                    final prefs = notificationController.preferences.value;
                    return _buildSettingsItem(
                      context,
                      icon: Icons.smart_toy_outlined,
                      title: 'Personalized AI Features',
                      subtitle:
                          'Allow AI to personalize suggestions and content',
                      trailing: Switch.adaptive(
                        value: prefs?.inAppMessaging ?? true,
                        onChanged: (value) async {
                          if (prefs != null) {
                            final updatedPrefs = prefs.copyWith(
                              inAppMessaging: value,
                              updatedAt: DateTime.now(),
                            );
                            await notificationController
                                .updateNotificationPreferences(updatedPrefs);
                          }
                        },
                      ),
                    );
                  }),
                  Obx(() {
                    final prefs = notificationController.preferences.value;
                    return _buildSettingsItem(
                      context,
                      icon: Icons.analytics_outlined,
                      title: 'Analytics Sharing',
                      subtitle:
                          'Share anonymized usage data to improve the app',
                      trailing: Switch.adaptive(
                        value: prefs?.communityUpdates ?? true,
                        onChanged: (value) async {
                          if (prefs != null) {
                            final updatedPrefs = prefs.copyWith(
                              communityUpdates: value,
                              updatedAt: DateTime.now(),
                            );
                            await notificationController
                                .updateNotificationPreferences(updatedPrefs);
                          }
                        },
                      ),
                    );
                  }),

                  const SizedBox(height: 16),

                  // Data Management Actions
                  _buildSettingsItem(
                    context,
                    icon: Icons.download_outlined,
                    title: 'Download My Data',
                    subtitle: 'Get a PDF copy of your data',
                    onTap: () async {
                      await _requestDataExport(
                        context,
                        userManagementController,
                      );
                    },
                  ),
                  _buildSettingsItem(
                    context,
                    icon: Icons.delete_outline,
                    title: 'Delete My Account',
                    subtitle: 'Permanently delete your account and all data',
                    onTap: () async {
                      await _showDeleteAccountDialog(
                        context,
                        userManagementController,
                      );
                    },
                  ),

                  const SizedBox(height: 32),

                  // Logout Button
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: TextButton(
                      onPressed: () {
                        _showLogoutDialog(context, authController);
                      },
                      child: Text(
                        'Logout',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.red,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  // Add bottom padding for floating tab bar
                  const SizedBox(height: 100),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildSettingsItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    Widget? trailing,
  }) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: theme.colorScheme.primary, size: 20),
        ),
        title: Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        trailing:
            trailing ??
            Icon(
              Icons.chevron_right,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
            ),
        onTap: onTap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, AuthController authController) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Logout',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: theme.textTheme.bodyLarge,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                authController.logout();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _requestDataExport(
    BuildContext context,
    UserManagementController userManagementController,
  ) async {
    try {
      final success = await userManagementController.downloadUserDataPdf();
      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('PDF downloaded successfully to Downloads folder.'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to request data export. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('An error occurred. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showDeleteAccountDialog(
    BuildContext context,
    UserManagementController userManagementController,
  ) async {
    final theme = Theme.of(context);

    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: Text(
              'Delete Account?',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'This will permanently delete your account and all associated data, including:',
                ),
                const SizedBox(height: 12),
                const Text('• Profile information'),
                const Text('• Habits and tasks'),
                const Text('• Progress history'),
                const Text('• Achievements and rewards'),
                const Text('• All other app data'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                    ),
                  ),
                  child: const Text(
                    '⚠️ This action cannot be undone. Are you absolutely sure?',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(ctx, false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(ctx, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Delete Account'),
              ),
            ],
          ),
    );

    if (confirmed == true && context.mounted) {
      try {
        final success = await userManagementController.deleteAccount();
        if (success && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Account deletion request submitted.'),
              backgroundColor: Colors.green,
            ),
          );
          // The AuthController should handle logout after successful deletion
        } else if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete account. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('An error occurred. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
