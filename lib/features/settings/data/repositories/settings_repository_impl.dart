import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/entities/theme_entity.dart';
import '../../domain/entities/language_entity.dart';
import '../../domain/repositories/settings_repository.dart';
import '../datasources/settings_remote_data_source.dart';
import '../models/feedback_model.dart';
import 'package:url_launcher/url_launcher.dart';

/// Implementation of settings repository
class SettingsRepositoryImpl implements SettingsRepository {
  final SettingsRemoteDataSource remoteDataSource;

  SettingsRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, ThemeEntity>> getThemePreferences() async {
    try {
      final themeModel = await remoteDataSource.getThemePreferences();
      return Right(themeModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return const Left(ServerFailure(message: 'Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, ThemeEntity>> updateThemePreferences({
    required String themeMode,
  }) async {
    try {
      final themeModel = await remoteDataSource.updateThemePreferences(
        themeMode: themeMode,
      );
      return Right(themeModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, LanguageEntity>> getLanguagePreferences() async {
    try {
      final languageModel = await remoteDataSource.getLanguagePreferences();
      return Right(languageModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return const Left(ServerFailure(message: 'Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, LanguageEntity>> updateLanguagePreferences({
    required String languageCode,
    required String countryCode,
  }) async {
    try {
      final languageModel = await remoteDataSource.updateLanguagePreferences(
        languageCode: languageCode,
        countryCode: countryCode,
      );
      return Right(languageModel.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return const Left(ServerFailure(message: 'Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, bool>> submitFeedback({
    required String type,
    required String title,
    required String description,
    String? priority,
    String? platform,
    String? appVersion,
  }) async {
    try {
      final feedbackModel = FeedbackModel(
        type: type,
        title: title,
        description: description,
        priority: priority,
        platform: platform,
        appVersion: appVersion,
      );

      final result = await remoteDataSource.submitFeedback(feedbackModel);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on UnauthorizedException catch (e) {
      return Left(AuthFailure(message: e.message));
    } catch (e) {
      return const Left(ServerFailure(message: 'Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, bool>> rateApp() async {
    try {
      // For app rating, we'll use the device's native app store
      const String appStoreUrl =
          'https://apps.apple.com/app/power-up/id123456789';
      const String playStoreUrl =
          'https://play.google.com/store/apps/details?id=com.powerup.app';

      // Try to launch the appropriate store URL
      final Uri url = Uri.parse(playStoreUrl); // Default to Play Store
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
        return const Right(true);
      } else {
        return const Left(ServerFailure(message: 'Could not launch app store'));
      }
    } catch (e) {
      return Left(
        ServerFailure(message: 'Failed to open app store: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> openHelp() async {
    try {
      await remoteDataSource.getHelpArticles();
      return const Right(true);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to load help articles'));
    }
  }

  @override
  Future<Either<Failure, String>> getTermsOfService() async {
    try {
      final content = await remoteDataSource.getTermsOfService();
      return Right(content);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to load terms of service'));
    }
  }

  @override
  Future<Either<Failure, String>> getPrivacyPolicy() async {
    try {
      final content = await remoteDataSource.getPrivacyPolicy();
      return Right(content);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to load privacy policy'));
    }
  }

  @override
  Future<Either<Failure, String>> getAboutUs() async {
    try {
      final content = await remoteDataSource.getAboutUs();
      return Right(content);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to load about us content'));
    }
  }
}
