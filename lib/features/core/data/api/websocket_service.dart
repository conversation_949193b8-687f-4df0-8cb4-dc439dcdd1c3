import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;

import '../../../../core/util/app_constants.dart';

/// Service for handling WebSocket connections for real-time communication
class WebSocketService {
  WebSocketChannel? _channel;
  StreamSubscription? _subscription;

  final StreamController<Map<String, dynamic>> _messageController =
      StreamController<Map<String, dynamic>>.broadcast();

  final StreamController<WebSocketConnectionState> _connectionController =
      StreamController<WebSocketConnectionState>.broadcast();

  /// Current connection state
  WebSocketConnectionState _connectionState =
      WebSocketConnectionState.disconnected;

  /// Authentication token for the WebSocket connection
  String? _authToken;

  /// Reconnection attempt counter
  int _reconnectAttempts = 0;

  /// Maximum reconnection attempts
  static const int _maxReconnectAttempts = 5;

  /// Reconnection delay in seconds
  static const int _reconnectDelaySeconds = 2;

  /// Stream of incoming messages
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;

  /// Stream of connection state changes
  Stream<WebSocketConnectionState> get connectionStateStream =>
      _connectionController.stream;

  /// Current connection state
  WebSocketConnectionState get connectionState => _connectionState;

  /// Whether the WebSocket is connected
  bool get isConnected =>
      _connectionState == WebSocketConnectionState.connected;

  /// Connect to WebSocket server
  Future<void> connect({String? authToken}) async {
    if (_connectionState == WebSocketConnectionState.connecting ||
        _connectionState == WebSocketConnectionState.connected) {
      return;
    }

    _authToken = authToken;
    _setConnectionState(WebSocketConnectionState.connecting);

    try {
      // Build WebSocket URL - replace http with ws/wss
      const baseUrl = AppConstants.baseUrl;
      final wsUrl = baseUrl
          .replaceFirst('http://', 'ws://')
          .replaceFirst('https://', 'wss://');

      final uri = Uri.parse('$wsUrl/messaging');

      if (kDebugMode) {
        print('WebSocketService: Connecting to $uri');
      }

      // Add auth token to connection headers if available
      final headers = <String, String>{};
      if (_authToken != null) {
        headers['Authorization'] = 'Bearer $_authToken';
      }

      _channel = WebSocketChannel.connect(uri, protocols: null);

      // Listen for messages
      _subscription = _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      _setConnectionState(WebSocketConnectionState.connected);
      _reconnectAttempts = 0;

      if (kDebugMode) {
        print('WebSocketService: Connected successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('WebSocketService: Connection error: $e');
      }
      _setConnectionState(WebSocketConnectionState.error);
      _scheduleReconnect();
    }
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    if (kDebugMode) {
      print('WebSocketService: Disconnecting...');
    }

    _setConnectionState(WebSocketConnectionState.disconnecting);

    await _subscription?.cancel();
    _subscription = null;

    await _channel?.sink.close(status.goingAway);
    _channel = null;

    _setConnectionState(WebSocketConnectionState.disconnected);
    _reconnectAttempts = 0;
  }

  /// Send a message through the WebSocket
  void sendMessage(Map<String, dynamic> message) {
    if (!isConnected) {
      if (kDebugMode) {
        print('WebSocketService: Cannot send message - not connected');
      }
      return;
    }

    try {
      final jsonMessage = jsonEncode(message);
      _channel?.sink.add(jsonMessage);

      if (kDebugMode) {
        print('WebSocketService: Sent message: $jsonMessage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('WebSocketService: Error sending message: $e');
      }
    }
  }

  /// Send a direct message to a user
  void sendDirectMessage({
    required String recipientId,
    required String content,
    List<String>? attachments,
  }) {
    sendMessage({
      'type': 'direct_message',
      'data': {
        'recipientId': recipientId,
        'content': content,
        'attachments': attachments ?? [],
      },
    });
  }

  /// Send a message to a challenge
  void sendChallengeMessage({
    required String challengeId,
    required String content,
    List<String>? attachments,
  }) {
    sendMessage({
      'type': 'challenge_message',
      'data': {
        'challengeId': challengeId,
        'content': content,
        'attachments': attachments ?? [],
      },
    });
  }

  /// Send a group message
  void sendGroupMessage({
    required String groupId,
    required String content,
    List<String>? attachments,
  }) {
    sendMessage({
      'type': 'group_message',
      'data': {
        'groupId': groupId,
        'content': content,
        'attachments': attachments ?? [],
      },
    });
  }

  /// Join a group/room for real-time communication
  void joinGroup(String groupId) {
    sendMessage({'type': 'join_group', 'groupId': groupId});
  }

  /// Leave a group/room
  void leaveGroup(String groupId) {
    sendMessage({'type': 'leave_group', 'groupId': groupId});
  }

  /// Set authentication token for WebSocket connection
  void setAuthToken(String token) {
    _authToken = token;
  }

  /// Handle incoming messages
  void _onMessage(dynamic message) {
    try {
      final Map<String, dynamic> parsedMessage = jsonDecode(message as String);

      if (kDebugMode) {
        print('WebSocketService: Received message: $parsedMessage');
      }

      _messageController.add(parsedMessage);
    } catch (e) {
      if (kDebugMode) {
        print('WebSocketService: Error parsing message: $e');
      }
    }
  }

  /// Handle WebSocket errors
  void _onError(dynamic error) {
    if (kDebugMode) {
      print('WebSocketService: Error occurred: $error');
    }

    _setConnectionState(WebSocketConnectionState.error);
    _scheduleReconnect();
  }

  /// Handle WebSocket disconnection
  void _onDisconnected() {
    if (kDebugMode) {
      print('WebSocketService: Disconnected');
    }

    if (_connectionState != WebSocketConnectionState.disconnecting) {
      _setConnectionState(WebSocketConnectionState.disconnected);
      _scheduleReconnect();
    }
  }

  /// Schedule automatic reconnection
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      if (kDebugMode) {
        print('WebSocketService: Max reconnect attempts reached');
      }
      return;
    }

    _reconnectAttempts++;

    if (kDebugMode) {
      print(
        'WebSocketService: Scheduling reconnect attempt $_reconnectAttempts',
      );
    }

    Timer(Duration(seconds: _reconnectDelaySeconds * _reconnectAttempts), () {
      if (_connectionState == WebSocketConnectionState.disconnected ||
          _connectionState == WebSocketConnectionState.error) {
        connect(authToken: _authToken);
      }
    });
  }

  /// Set connection state and notify listeners
  void _setConnectionState(WebSocketConnectionState state) {
    if (_connectionState != state) {
      _connectionState = state;
      _connectionController.add(state);
    }
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _messageController.close();
    _connectionController.close();
  }
}

/// WebSocket connection states
enum WebSocketConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error,
}
