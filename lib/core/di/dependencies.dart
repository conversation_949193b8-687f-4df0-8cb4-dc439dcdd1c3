import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import '../network/network_info.dart';
import '../network/network_info_impl.dart';
import '../services/notification_service.dart';
import '../services/sync_data_service.dart';
import '../services/data_sync_service.dart';
import '../services/secure_storage_service.dart';
import '../services/auth_service.dart';
import '../services/theme_service.dart';
import '../util/app_constants.dart';
import '../../features/core/data/api/api_client.dart';
import '../../features/core/data/api/interceptors/auth_interceptor.dart';
import '../../features/core/data/api/interceptors/error_interceptor.dart';
import '../../features/core/data/api/websocket_service.dart';
import '../../features/core/data/local/storage_service.dart';
import '../../features/authentication/di/auth_bindings.dart';
import '../../features/habits/di/habits_bindings.dart';
import '../../features/tasks/di/tasks_bindings.dart';
import '../../features/calendar/di/calendar_bindings.dart';
import '../../features/notifications/di/notification_bindings.dart';
import '../../features/gamification/di/gamification_bindings.dart';
import '../../features/statistics/di/statistics_bindings.dart';
import '../../features/tasks/data/datasources/task_local_data_source.dart';
import '../../features/tasks/data/datasources/task_remote_data_source.dart';
import '../../features/habits/data/datasources/habit_local_data_source.dart';
import '../../features/habits/data/datasources/habit_remote_data_source.dart';
import '../../features/user_management/di/user_management_bindings.dart';

/// Initialize all application dependencies
Future<void> initDependencies() async {
  // Core dependencies
  await _initCoreDependencies();

  // Core services
  await _initServices();

  // Feature specific dependencies
  await _initAuthDependencies();
  await _initHabitsDependencies();
  await _initTasksDependencies();
  await _initCalendarDependencies();
  await _initNotificationDependencies();
  await _initGamificationDependencies();
  await _initUserManagementDependencies();
  await _initStatisticsDependencies();
  await _initPodcastsDependencies();

  // Initialize advanced data sync service (after all feature dependencies are ready)
  await _initDataSyncService();

  // Initialize the advanced data sync service
  await _initDataSyncService();
}

/// Initialize core dependencies that are used across multiple features
Future<void> _initCoreDependencies() async {
  // Network
  Get.lazyPut<InternetConnectionChecker>(
    () => InternetConnectionChecker(),
    fenix: true,
  );

  Get.lazyPut<NetworkInfo>(
    () => NetworkInfoImpl(Get.find<InternetConnectionChecker>()),
    fenix: true,
  );

  // API Client
  final dio = Dio(
    BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: const Duration(
        milliseconds: AppConstants.apiConnectTimeout,
      ),
      receiveTimeout: const Duration(
        milliseconds: AppConstants.apiReceiveTimeout,
      ),
      contentType: 'application/json',
    ),
  );

  // Local Storage (needed for interceptors)
  final storageService = await StorageService.getInstance();
  Get.lazyPut<StorageService>(() => storageService, fenix: true);

  // Add interceptors
  dio.interceptors.add(AuthInterceptor(storageService: storageService));
  dio.interceptors.add(ErrorInterceptor());

  Get.lazyPut<Dio>(() => dio, fenix: true);
  Get.lazyPut<ApiClient>(() => ApiClient(Get.find<Dio>()), fenix: true);

  // WebSocket Service
  Get.lazyPut<WebSocketService>(() => WebSocketService(), fenix: true);

  // Flutter Local Notifications Plugin
  Get.lazyPut<FlutterLocalNotificationsPlugin>(
    () => FlutterLocalNotificationsPlugin(),
    fenix: true,
  );

  // Firebase Messaging
  Get.lazyPut<FirebaseMessaging>(() => FirebaseMessaging.instance, fenix: true);

  // Secure Storage Service
  Get.lazyPut<SecureStorageService>(() => SecureStorageService(), fenix: true);
}

/// Initialize application-wide services
Future<void> _initServices() async {
  // Auth service
  Get.lazyPut<AuthService>(() => AuthService(), fenix: true);

  // Theme service
  Get.lazyPut<ThemeService>(
    () => ThemeService(Get.find<StorageService>()),
    fenix: true,
  );

  // Notification service
  final notificationService = NotificationService();
  Get.lazyPut<NotificationService>(() => notificationService, fenix: true);

  // Basic data sync service (old)
  final syncDataService = SyncDataService();
  Get.lazyPut<SyncDataService>(() => syncDataService, fenix: true);

  // Advanced data sync service (new)
  // Will be initialized later after task and habit dependencies are registered
}

/// Initialize authentication dependencies
Future<void> _initAuthDependencies() async {
  // Initialize auth bindings
  final authBindings = AuthBindings();
  authBindings.dependencies();
}

/// Initialize habits feature dependencies
Future<void> _initHabitsDependencies() async {
  // Initialize habits bindings
  final habitsBindings = HabitsBindings();
  habitsBindings.dependencies();
}

/// Initialize tasks feature dependencies
Future<void> _initTasksDependencies() async {
  // Initialize tasks bindings
  final tasksBindings = TasksBindings();
  tasksBindings.dependencies();
}

/// Initialize calendar feature dependencies
Future<void> _initCalendarDependencies() async {
  // Initialize calendar bindings
  final calendarBindings = CalendarBindings();
  calendarBindings.dependencies();
}

/// Initialize notification feature dependencies
Future<void> _initNotificationDependencies() async {
  // Initialize notification bindings
  final notificationBindings = NotificationBindings();
  notificationBindings.dependencies();
}

/// Initialize gamification feature dependencies
Future<void> _initGamificationDependencies() async {
  // Initialize gamification bindings
  final gamificationBindings = GamificationBindings();
  gamificationBindings.dependencies();
}

/// Initialize user management feature dependencies
Future<void> _initUserManagementDependencies() async {
  // Initialize user management bindings
  final userManagementBindings = UserManagementBindings();
  userManagementBindings.dependencies();
}

/// Initialize podcasts feature dependencies
Future<void> _initPodcastsDependencies() async {
  // Will be implemented in the Podcasts task
}

/// Initialize statistics feature dependencies
Future<void> _initStatisticsDependencies() async {
  // Initialize statistics bindings
  final statisticsBindings = StatisticsBindings();
  statisticsBindings.dependencies();
}

/// Initialize the advanced data sync service (must be called after all feature dependencies are ready)
Future<void> _initDataSyncService() async {
  // Register DataSyncService with proper dependencies
  Get.lazyPut<DataSyncService>(
    () => DataSyncService(
      taskLocalDataSource: Get.find<TaskLocalDataSource>(),
      taskRemoteDataSource: Get.find<TaskRemoteDataSource>(),
      habitLocalDataSource: Get.find<HabitLocalDataSource>(),
      habitRemoteDataSource: Get.find<HabitRemoteDataSource>(),
      networkInfo: Get.find<NetworkInfo>(),
    ),
    fenix: true,
  );

  // Start periodic sync
  // We'll use a simple check to see if we're logged in
  // In a real app, you would check the auth state more robustly
  try {
    final dataSyncService = Get.find<DataSyncService>();
    dataSyncService.startPeriodicSync();
  } catch (e) {
    // Error during sync service initialization, will be handled when user logs in
  }
}
