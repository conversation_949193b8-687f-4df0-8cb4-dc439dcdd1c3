/// Constants used throughout the app
class AppConstants {
  /// Private constructor to prevent instantiation
  AppConstants._();

  // API constants
  static const String baseUrl = 'http://localhost:3000/api';
  static const int apiConnectTimeout = 5000;
  static const int apiReceiveTimeout = 10000;

  // Storage keys
  static const String storageUserToken = 'user_token';
  static const String storageUserId = 'user_id';
  static const String storageUserEmail = 'user_email';
  static const String storageUserProfile = 'user_profile';
  static const String storageThemeMode = 'theme_mode';

  // Feature flags
  static const bool enableSocialLogin = true;
  static const bool enablePushNotifications = true;
  static const bool enablePremiumFeatures = true;

  // App settings
  static const int defaultPageSize = 20;
  static const String appVersion = '1.0.0';
}
