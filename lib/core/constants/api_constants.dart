/// API constants for network requests
class ApiConstants {
  /// Private constructor to prevent instantiation
  ApiConstants._();

  // Base URL for the API
  static const String baseUrl = 'http://localhost:3000/api';

  // Timeout configurations
  static const int connectTimeout = 5000;
  static const int receiveTimeout = 3000;

  // API Endpoints
  // Auth
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String refreshTokenEndpoint = '/auth/refresh';
  static const String logoutEndpoint = '/auth/logout';
  static const String forgotPasswordEndpoint = '/auth/forgot-password';
  static const String resetPasswordEndpoint = '/auth/reset-password';

  // Users
  static const String usersEndpoint = '/users';
  static const String profileEndpoint = '/users/profile';

  // Notifications
  static const String notificationsEndpoint = '/notifications';
  static const String devicesEndpoint = '/devices';
  static const String preferencesEndpoint = '/preferences';
  static const String pushNotificationEndpoint = '/notifications/push';
  static const String testNotificationEndpoint = '/notifications/test';

  // Tasks
  static const String tasksEndpoint = '/tasks';

  // Habits
  static const String habitsEndpoint = '/habits';

  // Calendar
  static const String calendarEndpoint = '/calendar';

  // Podcasts
  static const String podcastsEndpoint = '/podcasts';

  // Community
  static const String communityEndpoint = '/community';

  // Statistics
  static const String statisticsEndpoint = '/statistics';
}
