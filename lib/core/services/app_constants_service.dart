import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../../features/app_constants/data/models/app_constants_model.dart';
import '../../features/app_constants/domain/entities/app_constants_entity.dart';
import '../../features/app_constants/domain/usecases/clear_app_constants_cache_usecase.dart';
import '../../features/app_constants/domain/usecases/get_app_constants_usecase.dart';
import '../../features/app_constants/domain/usecases/get_cached_app_constants_usecase.dart';
import '../usecases/usecase.dart';

/// Service for managing app constants globally
class AppConstantsService extends GetxService {
  final GetAppConstantsUseCase _getAppConstantsUseCase;
  final GetCachedAppConstantsUseCase _getCachedAppConstantsUseCase;
  final ClearAppConstantsCacheUseCase _clearAppConstantsCacheUseCase;

  AppConstantsService({
    required GetAppConstantsUseCase getAppConstantsUseCase,
    required GetCachedAppConstantsUseCase getCachedAppConstantsUseCase,
    required ClearAppConstantsCacheUseCase clearAppConstantsCacheUseCase,
  }) : _getAppConstantsUseCase = getAppConstantsUseCase,
       _getCachedAppConstantsUseCase = getCachedAppConstantsUseCase,
       _clearAppConstantsCacheUseCase = clearAppConstantsCacheUseCase;

  // Observable app constants
  final Rx<AppConstantsEntity?> _appConstants = Rx<AppConstantsEntity?>(null);
  final RxBool _isLoading = false.obs;
  final RxBool _isInitialized = false.obs;
  final RxString _errorMessage = ''.obs;

  // Getters
  AppConstantsEntity? get appConstants => _appConstants.value;
  bool get isLoading => _isLoading.value;
  bool get isInitialized => _isInitialized.value;
  String get errorMessage => _errorMessage.value;

  // Reactive getters for UI
  Rx<AppConstantsEntity?> get appConstantsRx => _appConstants;
  RxBool get isLoadingRx => _isLoading;
  RxBool get isInitializedRx => _isInitialized;
  RxString get errorMessageRx => _errorMessage;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeConstants();
  }

  /// Initialize app constants on service startup
  Future<void> _initializeConstants() async {
    if (kDebugMode) {
      print('Initializing app constants service...');
    }

    // First, try to get cached constants for immediate availability
    final cachedConstants = _getCachedAppConstantsUseCase();
    if (cachedConstants != null) {
      _appConstants.value = cachedConstants;
      if (kDebugMode) {
        print('Loaded cached app constants');
      }
    } else {
      // If no cache, use default constants
      _appConstants.value = AppConstantsModel.withDefaults();
      if (kDebugMode) {
        print('Using default app constants');
      }
    }

    _isInitialized.value = true;

    // Then fetch fresh constants from the backend
    await refreshConstants();
  }

  /// Refresh app constants from the backend
  Future<void> refreshConstants() async {
    if (kDebugMode) {
      print('Refreshing app constants from backend...');
    }

    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final result = await _getAppConstantsUseCase(NoParams());

      result.fold(
        (failure) {
          _errorMessage.value = failure.message;
          if (kDebugMode) {
            print('Failed to refresh app constants: ${failure.message}');
          }
        },
        (constants) {
          _appConstants.value = constants;
          if (kDebugMode) {
            print('Successfully refreshed app constants');
          }
        },
      );
    } catch (e) {
      _errorMessage.value = 'Unexpected error occurred';
      if (kDebugMode) {
        print('Unexpected error while refreshing constants: $e');
      }
    } finally {
      _isLoading.value = false;
    }
  }

  /// Clear cached constants and refresh
  Future<void> clearCacheAndRefresh() async {
    if (kDebugMode) {
      print('Clearing cache and refreshing app constants...');
    }

    await _clearAppConstantsCacheUseCase();
    await refreshConstants();
  }

  // Convenience methods for accessing constants

  /// Get string constant
  String getString(String key, [String? defaultValue]) {
    return _appConstants.value?.getString(key, defaultValue) ??
        defaultValue ??
        '';
  }

  /// Get boolean constant
  bool getBool(String key, [bool defaultValue = false]) {
    return _appConstants.value?.getBool(key, defaultValue) ?? defaultValue;
  }

  /// Get integer constant
  int getInt(String key, [int defaultValue = 0]) {
    return _appConstants.value?.getInt(key, defaultValue) ?? defaultValue;
  }

  /// Get double constant
  double getDouble(String key, [double defaultValue = 0.0]) {
    return _appConstants.value?.getDouble(key, defaultValue) ?? defaultValue;
  }

  /// Get list constant
  List<T>? getList<T>(String key, [List<T>? defaultValue]) {
    return _appConstants.value?.getList<T>(key, defaultValue) ?? defaultValue;
  }

  /// Get map constant
  Map<String, dynamic>? getMap(
    String key, [
    Map<String, dynamic>? defaultValue,
  ]) {
    return _appConstants.value?.getMap(key, defaultValue) ?? defaultValue;
  }

  /// Get any constant with type safety
  T? getConstant<T>(String key, [T? defaultValue]) {
    return _appConstants.value?.getConstant<T>(key, defaultValue) ??
        defaultValue;
  }

  // Feature flags convenience methods
  bool get enableSocialLogin => getBool('features.socialLogin', true);
  bool get enablePushNotifications =>
      getBool('features.pushNotifications', true);
  bool get enablePremiumFeatures => getBool('features.premiumFeatures', true);
  bool get enableDarkMode => getBool('features.darkMode', true);
  bool get enableOfflineMode => getBool('features.offlineMode', false);

  // API configuration convenience methods
  String get apiBaseUrl =>
      getString('api.baseUrl', 'http://localhost:3000/api');
  int get apiConnectTimeout => getInt('api.connectTimeout', 5000);
  int get apiReceiveTimeout => getInt('api.receiveTimeout', 10000);

  // UI configuration convenience methods
  String get appName => getString('ui.appName', 'Power Up');
  String get appVersion => getString('ui.appVersion', '1.0.0');
  double get splashLogoSize => getDouble('ui.splashLogoSize', 120.0);
  double get appBarLogoHeight => getDouble('ui.appBarLogoHeight', 32.0);
  double get pageSpacing => getDouble('ui.pageSpacing', 24.0);
  double get sectionSpacing => getDouble('ui.sectionSpacing', 16.0);

  // Gamification convenience methods
  int get xpPerHabit => getInt('gamification.xpPerHabit', 10);
  int get xpPerTask => getInt('gamification.xpPerTask', 15);
  int get xpPerChallenge => getInt('gamification.xpPerChallenge', 50);
  int get levelUpThreshold => getInt('gamification.levelUpThreshold', 100);

  // Limits convenience methods
  int get maxHabitsPerUser => getInt('limits.maxHabitsPerUser', 50);
  int get maxTasksPerUser => getInt('limits.maxTasksPerUser', 100);
  int get maxChallengesPerUser => getInt('limits.maxChallengesPerUser', 10);

  // Store URLs convenience methods
  String get appStoreUrl => getString(
    'store.appStoreUrl',
    'https://apps.apple.com/app/power-up/id123456789',
  );
  String get playStoreUrl => getString(
    'store.playStoreUrl',
    'https://play.google.com/store/apps/details?id=com.example.powerup',
  );
}
