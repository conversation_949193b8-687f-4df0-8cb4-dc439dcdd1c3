name: power_up
description: "A productivity and self-improvement app with AI coaching"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2
  flutter: ">=3.10.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Core packages
  get: ^4.7.2 # For state management and dependency injection
  get_storage: ^2.1.1 # GetX local storage solution
  dartz: ^0.10.1 # For functional programming features
  dio: ^5.3.0 # For HTTP requests
  freezed_annotation: ^2.4.1 # For immutable models
  json_annotation: ^4.8.1 # For JSON serialization

  # Firebase
  firebase_core: ^3.13.1 # Firebase core functionality
  firebase_auth: ^5.5.4 # Firebase authentication
  firebase_messaging: ^15.2.6 # Push notifications
  firebase_in_app_messaging: ^0.8.1+6 # In-app messaging

  # Social authentication
  google_sign_in: ^6.3.0 # Google Sign-In
  sign_in_with_apple: ^7.0.1 # Apple Sign-In

  # Audio
  just_audio: ^0.9.40 # Audio playback

  # Monetization
  purchases_flutter: ^8.8.1 # RevenueCat for in-app purchases

  # Notifications
  flutter_local_notifications: ^17.2.4 # Local notifications
  timezone: ^0.9.4 # Timezone support for notifications

  # Other
  web_socket_channel: ^2.4.0 # WebSocket communication
  table_calendar: ^3.0.9 # Calendar widget
  internet_connection_checker: ^1.0.0+1 # Check internet connectivity
  equatable: ^2.0.5 # For value equality
  flutter_secure_storage: ^9.2.2 # Secure storage for tokens
  timeago: ^3.7.0 # Time ago formatting
  fl_chart: ^0.68.0 # Charts and data visualization
  url_launcher: ^6.2.4 # For launching URLs
  flutter_html: ^3.0.0-beta.2 # HTML rendering for help articles
  path_provider: ^2.1.1 # For accessing device directories
  permission_handler: ^11.0.1 # For handling permissions

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.3 # Linting rules
  mockito: ^5.4.4 # Mocking for tests
  mocktail: ^1.0.3 # Alternative mocking framework
  build_runner: ^2.4.8 # Code generation
  freezed: ^2.4.1 # Code generation for immutable models
  json_serializable: ^6.7.0 # JSON serialization
  flutter_launcher_icons: ^0.14.1 # App icon generation

# App icon configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/logo.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/images/logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/logo.png"

  # For information on the generic Dart part of this file, see the
  # following page: https://dart.dev/tools/pub/pubspec

  # The following section is specific to Flutter packages.
  async: any
  stream_channel: any
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/audio/ambient/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
